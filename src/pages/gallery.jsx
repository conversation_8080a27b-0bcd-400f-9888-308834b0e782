import React, {Component} from 'react';
import {View, Text, StyleSheet} from '@hippy/react';

// Skia Canvas 波形图组件 - 第一个
class SkiaCanvasWaveform extends Component {
    constructor(props) {
        super(props);
        this.state = {
            canvasData: null,
        };
        this.animationId = null;
        this.waveData = [];
    }

    componentDidMount() {
        this.initCanvas();
    }

    componentWillUnmount() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    initCanvas() {
        const { width = 300, height = 200 } = this.props;
        
        // 初始化波形数据
        const bars = 50;
        for (let i = 0; i < bars; i++) {
            this.waveData.push(Math.random() * (height - 40) + 20);
        }
        
        this.startAnimation();
    }

    startAnimation() {
        const animate = () => {
            this.updateWaveData();
            this.drawWaveform();
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate();
    }

    updateWaveData() {
        const { height = 200 } = this.props;
        
        // 移除第一个数据点，在末尾添加新的
        this.waveData.shift();
        this.waveData.push(Math.random() * (height - 40) + 20);
    }

    drawWaveform() {
        const { width = 300, height = 200 } = this.props;
        
        try {
            // 使用 Skia Canvas
            const { createCanvas } = require('skia-canvas');
            const canvas = createCanvas(width, height);
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, width, height);
            
            // 绘制背景
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制波形
            const bars = this.waveData.length;
            const barWidth = width / bars;
            
            // 创建渐变
            const gradient = ctx.createLinearGradient(0, height, 0, 0);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            ctx.fillStyle = gradient;
            
            // 绘制波形条
            for (let i = 0; i < bars; i++) {
                const barHeight = this.waveData[i];
                const x = i * barWidth;
                const y = height - barHeight;
                
                ctx.fillRect(x + 1, y, barWidth - 2, barHeight);
            }
            
            // 转换为 base64 数据
            const dataUrl = canvas.toDataURL();
            this.setState({ canvasData: dataUrl });
            
        } catch (error) {
            console.log('Skia Canvas not available:', error);
            this.setState({ canvasData: null });
        }
    }

    render() {
        const { width = 300, height = 200, style } = this.props;
        const { canvasData } = this.state;

        return (
            <View style={[style, { width, height }]}>
                <img
                    src={canvasData}
                    style={{ width: '100%', height: '100%', borderRadius: 8 }}
                    alt="Skia Canvas Waveform"
                />
            </View>
        );
    }
}

// Skia Canvas 波形图组件 - 第二个（圆形波形）
class SkiaCanvasCircularWaveform extends Component {
    constructor(props) {
        super(props);
        this.state = {
            canvasData: null,
        };
        this.animationId = null;
        this.waveData = [];
        this.rotation = 0;
    }

    componentDidMount() {
        this.initCanvas();
    }

    componentWillUnmount() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    initCanvas() {
        const { width = 300, height = 300 } = this.props;

        // 初始化圆形波形数据
        const segments = 60;
        for (let i = 0; i < segments; i++) {
            this.waveData.push(Math.random() * 50 + 30);
        }

        this.startAnimation();
    }

    startAnimation() {
        const animate = () => {
            this.updateWaveData();
            this.drawCircularWaveform();
            this.animationId = requestAnimationFrame(animate);
        };

        animate();
    }

    updateWaveData() {
        // 更新波形数据和旋转角度
        this.waveData = this.waveData.map(() => Math.random() * 50 + 30);
        this.rotation += 0.02;
    }

    drawCircularWaveform() {
        const { width = 300, height = 300 } = this.props;

        try {
            // 使用 Skia Canvas
            const { createCanvas } = require('skia-canvas');
            const canvas = createCanvas(width, height);
            const ctx = canvas.getContext('2d');

            // 清除画布
            ctx.clearRect(0, 0, width, height);

            // 绘制背景
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(0, 0, width, height);

            // 设置画布中心
            const centerX = width / 2;
            const centerY = height / 2;
            const baseRadius = Math.min(width, height) / 4;

            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(this.rotation);

            // 绘制圆形波形
            const segments = this.waveData.length;
            const angleStep = (Math.PI * 2) / segments;

            for (let i = 0; i < segments; i++) {
                const angle = i * angleStep;
                const amplitude = this.waveData[i];

                // 计算起始和结束点
                const innerRadius = baseRadius;
                const outerRadius = baseRadius + amplitude;

                const x1 = Math.cos(angle) * innerRadius;
                const y1 = Math.sin(angle) * innerRadius;
                const x2 = Math.cos(angle) * outerRadius;
                const y2 = Math.sin(angle) * outerRadius;

                // 创建径向渐变
                const gradient = ctx.createRadialGradient(0, 0, innerRadius, 0, 0, outerRadius);
                const hue = (i / segments) * 360 + (this.rotation * 50);
                gradient.addColorStop(0, `hsla(${hue}, 70%, 50%, 0.8)`);
                gradient.addColorStop(1, `hsla(${hue + 60}, 70%, 70%, 0.3)`);

                // 绘制波形线段
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.strokeStyle = gradient;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.stroke();

                // 绘制外圈光点
                ctx.beginPath();
                ctx.arc(x2, y2, 2, 0, Math.PI * 2);
                ctx.fillStyle = `hsla(${hue}, 80%, 80%, 0.9)`;
                ctx.fill();
            }

            ctx.restore();

            // 绘制中心圆
            ctx.beginPath();
            ctx.arc(centerX, centerY, baseRadius * 0.3, 0, Math.PI * 2);
            const centerGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, baseRadius * 0.3);
            centerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
            centerGradient.addColorStop(1, 'rgba(100, 200, 255, 0.3)');
            ctx.fillStyle = centerGradient;
            ctx.fill();

            // 转换为 base64 数据
            const dataUrl = canvas.toDataURL();
            this.setState({ canvasData: dataUrl });

        } catch (error) {
            console.log('Skia Canvas not available:', error);
            this.setState({ canvasData: null });
        }
    }

    render() {
        const { width = 300, height = 300, style } = this.props;
        const { canvasData } = this.state;

        return (
            <View style={[style, { width, height }]}>
                <img
                    src={canvasData}
                    style={{ width: '100%', height: '100%', borderRadius: 8 }}
                    alt="Skia Canvas Circular Waveform"
                />
            </View>
        );
    }
}

export class Gallery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            bars: Array(100).fill(0).map(() => Math.random() * 100 + 20),
        };
        this.animationId = null;
    }

    componentDidMount() {
        this.animate();
    }

    componentWillUnmount() {
        if (this.animationId) {
            clearInterval(this.animationId);
        }
    }

    animate() {
        this.animationId = setInterval(() => {
            this.setState(prevState => {
                // 移除第一个，在末尾添加新的
                const newBars = [...prevState.bars.slice(1), Math.random() * 100 + 20];
                return {bars: newBars};
            });
        }, 16); // 60fps
    }

    render() {
        const {bars} = this.state;
        return (
            <View style={styles.container}>
                <Text style={styles.title}>DOM 波形图</Text>
                <View style={styles.waveform}>
                    {bars.map((height, index) => (
                        <View
                            key={index}
                            style={[
                                styles.waveformBar,
                                {height: height}
                            ]}
                        />
                    ))}
                </View>
                
                <Text style={styles.title}>Skia Canvas 波形图 - 条形</Text>
                <SkiaCanvasWaveform
                    width={300}
                    height={200}
                    style={styles.skiaCanvas}
                />

                <Text style={styles.title}>Skia Canvas 波形图 - 圆形</Text>
                <SkiaCanvasCircularWaveform
                    width={300}
                    height={300}
                    style={styles.skiaCanvasCircular}
                />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000',
    },
    title: {
        color: '#fff',
        fontSize: 16,
        margin: 10,
    },
    waveform: {
        flexDirection: 'row',
        alignItems: 'center',
        height: 200,
        marginBottom: 40,
    },
    waveformBar: {
        width: 2,
        backgroundColor: '#4c9afa',
        marginHorizontal: 2,
        borderRadius: 2,
    },
    skiaCanvas: {
        backgroundColor: '#111',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#333',
        marginBottom: 40,
    },
    skiaCanvasCircular: {
        backgroundColor: '#0a0a0a',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#333',
        marginBottom: 40,
    },
});

export default Gallery;