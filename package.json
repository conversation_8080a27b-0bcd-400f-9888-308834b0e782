{"name": "hippy-react-demo", "version": "2.0.0", "private": true, "main": "src/main.js", "author": "XQ Kuang <<EMAIL>>", "license": "Apache-2.0", "description": "Demo app for hippy-react", "repository": "https://github.com/Tencent/Hippy/tree/master/examples/hippy-react-demo", "scripts": {"serve": "webpack serve --config ./scripts/hippy-webpack.web.dev.js", "build": "webpack --config ./scripts/hippy-webpack.web.js", "hippy:debug": "hippy-debug", "hippy:dev": "hippy-dev -c ./scripts/hippy-webpack.dev.js", "hippy:vendor": "webpack --config ./scripts/hippy-webpack.ios-vendor.js --config ./scripts/hippy-webpack.android-vendor.js", "hippy:build": "webpack --config ./scripts/hippy-webpack.ios.js --config ./scripts/hippy-webpack.android.js"}, "keywords": ["Hippy", "React"], "dependencies": {"@hippy/react": "2.13.10", "@hippy/react-reconciler": "react17", "@hippy/react-web": "latest", "@hippy/rmc-list-view": "^1.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router": "~5.1.2", "react-router-dom": "~5.1.2", "regenerator-runtime": "^0.13.5", "skia-canvas": "^3.0.6", "swiper": "^6.7.0"}, "devDependencies": {"@babel/core": "^7.12.0", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-decorators": "^7.10.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-proposal-optional-chaining": "^7.10.4", "@babel/plugin-transform-async-to-generator": "^7.5.0", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/polyfill": "^7.12.0", "@babel/preset-env": "^7.12.0", "@babel/preset-react": "^7.9.4", "@hippy/debug-server-next": "^0.2.31", "@hippy/hippy-dynamic-import-plugin": "^2.0.0", "@hippy/hippy-hmr-plugin": "^0.0.2", "@hippy/hippy-react-refresh-webpack-plugin": "^0.5.5", "babel-loader": "^8.1.0", "bezier-easing": "^2.1.0", "case-sensitive-paths-webpack-plugin": "^2.2.0", "clean-webpack-plugin": "^4.0.0", "css-loader": "^3.5.2", "file-loader": "^5.1.0", "html-webpack-plugin": "^3.2.0", "react-refresh": "^0.11.0", "style-loader": "^1.1.4", "unicode-loader": "^1.0.7", "url-loader": "^4.0.0", "webpack": "^4.43.0", "webpack-cli": "^4.7.2", "webpack-dev-server": "^4.7.1"}}