import React, {Component} from 'react';
import {View, Text, StyleSheet} from '@hippy/react';

// Skia Canvas 波形图组件
class SkiaCanvasWaveform extends Component {
    constructor(props) {
        super(props);
        this.state = {
            canvasData: null,
        };
        this.animationId = null;
        this.waveData = [];
    }

    componentDidMount() {
        this.initCanvas();
    }

    componentWillUnmount() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    initCanvas() {
        const { width = 300, height = 200 } = this.props;
        
        // 初始化波形数据
        const bars = 50;
        for (let i = 0; i < bars; i++) {
            this.waveData.push(Math.random() * (height - 40) + 20);
        }
        
        this.startAnimation();
    }

    startAnimation() {
        const animate = () => {
            this.updateWaveData();
            this.drawWaveform();
            this.animationId = requestAnimationFrame(animate);
        };
        
        animate();
    }

    updateWaveData() {
        const { height = 200 } = this.props;
        
        // 移除第一个数据点，在末尾添加新的
        this.waveData.shift();
        this.waveData.push(Math.random() * (height - 40) + 20);
    }

    drawWaveform() {
        const { width = 300, height = 200 } = this.props;
        
        try {
            // 使用 Skia Canvas
            const { createCanvas } = require('skia-canvas');
            const canvas = createCanvas(width, height);
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, width, height);
            
            // 绘制背景
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制波形
            const bars = this.waveData.length;
            const barWidth = width / bars;
            
            // 创建渐变
            const gradient = ctx.createLinearGradient(0, height, 0, 0);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            ctx.fillStyle = gradient;
            
            // 绘制波形条
            for (let i = 0; i < bars; i++) {
                const barHeight = this.waveData[i];
                const x = i * barWidth;
                const y = height - barHeight;
                
                ctx.fillRect(x + 1, y, barWidth - 2, barHeight);
            }
            
            // 转换为 base64 数据
            const dataUrl = canvas.toDataURL();
            this.setState({ canvasData: dataUrl });
            
        } catch (error) {
            console.log('Skia Canvas not available:', error);
            this.setState({ canvasData: null });
        }
    }

    render() {
        const { width = 300, height = 200, style } = this.props;
        const { canvasData } = this.state;

        return (
            <View style={[style, { width, height }]}>
                <img
                    src={canvasData}
                    style={{ width: '100%', height: '100%', borderRadius: 8 }}
                    alt="Skia Canvas Waveform"
                />
            </View>
        );
    }
}

export class Gallery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            bars: Array(100).fill(0).map(() => Math.random() * 100 + 20),
        };
        this.animationId = null;
    }

    componentDidMount() {
        this.animate();
    }

    componentWillUnmount() {
        if (this.animationId) {
            clearInterval(this.animationId);
        }
    }

    animate() {
        this.animationId = setInterval(() => {
            this.setState(prevState => {
                // 移除第一个，在末尾添加新的
                const newBars = [...prevState.bars.slice(1), Math.random() * 100 + 20];
                return {bars: newBars};
            });
        }, 16); // 60fps
    }

    render() {
        const {bars} = this.state;
        return (
            <View style={styles.container}>
                <Text style={styles.title}>DOM 波形图</Text>
                <View style={styles.waveform}>
                    {bars.map((height, index) => (
                        <View
                            key={index}
                            style={[
                                styles.waveformBar,
                                {height: height}
                            ]}
                        />
                    ))}
                </View>
                
                <Text style={styles.title}>Skia Canvas 波形图</Text>
                <SkiaCanvasWaveform 
                    width={300} 
                    height={200} 
                    style={styles.skiaCanvas}
                />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000',
    },
    title: {
        color: '#fff',
        fontSize: 16,
        margin: 10,
    },
    waveform: {
        flexDirection: 'row',
        alignItems: 'center',
        height: 200,
        marginBottom: 40,
    },
    waveformBar: {
        width: 2,
        backgroundColor: '#4c9afa',
        marginHorizontal: 2,
        borderRadius: 2,
    },
    skiaCanvas: {
        backgroundColor: '#111',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#333',
        marginBottom: 40,
    },
});

export default Gallery;